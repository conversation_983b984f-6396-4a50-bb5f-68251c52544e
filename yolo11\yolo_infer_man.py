from unittest import result
from ultralytics import YOLO

if __name__ == "__main__":
    model = YOLO(r"C:\Users\<USER>\source\tobacco_det\runs\detect\train4\weights\best.pt")
    results = model.predict(
        r"C:\Users\<USER>\source\tobacco_det_data\data_yolo\val\images\tod_P061303_15.tif",
        conf=0.4,
        iou=0.4,
        show=True,
        line_width=1,
        imgsz=384,
        max_det=4096,
        show_labels=False,
        show_conf=True,
    )

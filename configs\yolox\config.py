_base_ = [
    "../../mmdetection/configs/yolox/yolox_m_8xb8-300e_coco.py",
]

metainfo = dict(
    classes=("object",),
    palette=[
        (
            220,
            20,
            60,
        ),
    ],
)


model = dict(
    bbox_head=dict(num_classes=1),
)

# dataset settings
data_root = r"C:\Users\<USER>\source\tobacco_det_data\data\\"
dataset_type = "CocoDataset"

backend_args = None

img_scale = (384, 384)

train_dataset = dict(
    dataset=dict(
        data_root=data_root,
        ann_file="annotations/train.json",
        data_prefix=dict(img="images/train/"),
        metainfo=metainfo,
    ),
)

train_dataloader = dict(
    batch_size=32,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(type="DefaultSampler", shuffle=True),
    dataset=train_dataset,
)

val_dataloader = dict(
    batch_size=8,
    num_workers=4,
    dataset=dict(
        data_root=data_root,
        ann_file="annotations/val.json",
        data_prefix=dict(img="images/val/"),
        metainfo=metainfo,
    ),
)
test_dataloader = val_dataloader

val_evaluator = dict(
    ann_file=data_root + "annotations/val.json",
)
test_evaluator = val_evaluator

# training settings
max_epochs = 300
num_last_epochs = 15
interval = 10

train_cfg = dict(max_epochs=max_epochs, val_interval=interval)

# optimizer
# default 8 gpu
base_lr = 0.01
optim_wrapper = dict(
    _delete_=True,
    type="AmpOptimWrapper",
    optimizer=dict(type="AdamW", lr=base_lr, betas=(0.9, 0.999), weight_decay=0.05),
)

# learning rate
param_scheduler = [
    dict(
        # use quadratic formula to warm up 5 epochs
        # and lr is updated by iteration
        # TODO: fix default scope in get function
        type="mmdet.QuadraticWarmupLR",
        by_epoch=True,
        begin=0,
        end=5,
        convert_to_iter_based=True,
    ),
    dict(
        # use cosine lr from 5 to 285 epoch
        type="CosineAnnealingLR",
        eta_min=base_lr * 0.05,
        begin=5,
        T_max=max_epochs - num_last_epochs,
        end=max_epochs - num_last_epochs,
        by_epoch=True,
        convert_to_iter_based=True,
    ),
    dict(
        # use fixed lr during last 15 epochs
        type="ConstantLR",
        by_epoch=True,
        factor=1,
        begin=max_epochs - num_last_epochs,
        end=max_epochs,
    ),
]

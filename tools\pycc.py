from pycocotools.coco import COCO
import numpy as np
from skimage import io  # scikit-learn 包
import matplotlib.pyplot as plt
import pylab

pylab.rcParams["figure.figsize"] = (8.0, 10.0)

# COCO 是一个类, 因此, 使用构造函数创建一个 COCO 对象, 构造函数首先会加载 json 文件,
# 然后解析图片和标注信息的 id, 根据 id 来创建其关联关系.

dataDir = r"C:\Users\<USER>\source\tobacco_det_data\batch2\images\train\\"
annFile = r"C:\Users\<USER>\source\tobacco_det_data\batch2\images\train.json"
# 初始化标注数据的 COCO api
coco = COCO(annFile)
print("数据加载成功！")

# 显示 COCO 数据集中的具体类和超类
cats = coco.loadCats(coco.getCatIds())
nms = [cat["name"] for cat in cats]
print("COCO categories: {}".format(" ".join(nms)))
print("类别总数为： %d" % len(nms))
nms = set([cat["supercategory"] for cat in cats])
print("COCO supercategories: {}".format(" ".join(nms)))
print("超类总数为：%d " % len(nms))


# 加载并显示指定 图片 id
catIds = coco.getCatIds(catNms=["object"])
imgIds = coco.getImgIds(catIds=catIds)
imgIds = coco.getImgIds(imgIds=[1])
img = coco.loadImgs(imgIds[0])[0]
print(img["file_name"])
I = io.imread(dataDir + img["file_name"])
plt.axis("off")
plt.imshow(I)
plt.show()

plt.imshow(I)
# 加载并将 “segmentation” 标注信息显示在图片上
# 加载并显示标注信息
plt.axis("off")
annIds = coco.getAnnIds(imgIds=img["id"], catIds=catIds, iscrowd=None)
anns = coco.loadAnns(annIds)
coco.showAnns(anns)

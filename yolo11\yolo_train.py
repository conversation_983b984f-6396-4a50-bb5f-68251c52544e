from ultralytics import YOLO

if __name__ == "__main__":
    # Load a model
    # model = YOLO("yolov8m.yaml")  # build a new model from scratch
    model = YOLO("yolo11m.pt")  # load a pretrained model (recommended for training)

    # Use the model
    model.train(
        data=r"C:\Users\<USER>\source\tobacco_det_data\data_yolo\data.yaml",
        epochs=300,
        imgsz=384,
        batch=8,
        cache="memory",
        device=[0],
        amp=True,
        cos_lr=True,
        dropout=0.1,
        flipud=0.5,
        fliplr=0.5,
        lr0=0.001,
    )  # train the model
    metrics = model.val()  # evaluate model performance on the validation set
    # results = model("https://ultralytics.com/images/bus.jpg")  # predict on an image
    # path = model.export(format="onnx")  # export the model to ONNX format

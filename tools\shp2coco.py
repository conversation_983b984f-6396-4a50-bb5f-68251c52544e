import argparse
import json
from pathlib import Path

import geopandas as gpd
import pandas as pd
import rasterio
from rasterio.transform import rowcol
from shapely.geometry import box


def convert_shp_to_coco(tiff_dir, shp_dir, output_file):
    tiff_dir = Path(tiff_dir)
    images = []
    annotations = []
    categories = [{"id": 1, "name": "object", "supercategory": "object"}]
    ann_id = 1
    img_id = 1

    # Process each TIFF file
    for tif_path in tiff_dir.glob("*.tif"):
        shp_path = Path(shp_dir) / f"{tif_path.stem}.shp"
        gdf = gpd.read_file(shp_path)

        print(f"Processing {tif_path.name}...")

        with rasterio.open(tif_path) as src:
            width, height = src.width, src.height
            transform = src.transform
            # Get the bounds of the TIFF in world coordinates
            tif_bounds = src.bounds  # (left, bottom, right, top)
            tif_crs = src.crs

        images.append({"id": img_id, "width": width, "height": height, "file_name": tif_path.name})

        # Create a bounding box geometry for the TIFF
        tif_bbox = box(tif_bounds.left, tif_bounds.bottom, tif_bounds.right, tif_bounds.top)

        # Ensure CRS compatibility
        if gdf.crs != tif_crs:
            print(f"Reprojecting features from {gdf.crs} to {tif_crs}")
            gdf = gdf.to_crs(tif_crs)
        else:
            gdf = gdf

        # Filter features that intersect with the TIFF bounds
        intersecting_features = gdf.clip(tif_bbox)
        # intersecting_features.to_file(f"temp/{tif_path.stem}_intersecting.shp")
        print(f"Found {len(intersecting_features)} features intersecting with {tif_path.name}")

        # Process each intersecting feature
        for idx, geom in enumerate(intersecting_features.geometry):
            if geom is None or geom.is_empty:
                continue

            # Use the clipped geometry bounds for bbox calculation
            xmin, ymin, xmax, ymax = geom.bounds

            # world to pixel (row, col)
            row_min, col_min = rowcol(transform, xmin, ymax)
            row_max, col_max = rowcol(transform, xmax, ymin)
            x, y = int(col_min), int(row_min)
            w, h = int(col_max - col_min), int(row_max - row_min)

            if w <= 0 or h <= 0:
                print(f"Invalid bbox for feature {idx} (w={w}, h={h}), skipping.")
                continue

            # Ensure bbox is within image bounds
            x = max(0, min(x, width - 1))
            y = max(0, min(y, height - 1))
            w = min(w, width - x)
            h = min(h, height - y)

            if w <= 0 or h <= 0:
                print(f"Invalid bbox for feature {idx} (w={w}, h={h}) after clipping, SHIT.")
                continue

            annotations.append(
                {
                    "id": ann_id,
                    "image_id": img_id,
                    "category_id": 1,
                    "bbox": [x, y, w, h],
                    "area": w * h,
                    "iscrowd": 0,
                }
            )
            ann_id += 1

        img_id += 1

    coco = {"images": images, "annotations": annotations, "categories": categories}

    with open(output_file, "w") as f:
        json.dump(coco, f, indent=4)
    print(f"COCO JSON saved to {output_file}")
    print(f"Total images: {len(images)}, Total annotations: {len(annotations)}")


def main():
    # parser = argparse.ArgumentParser(description="Convert TIFF+SHP to COCO JSON")
    # parser.add_argument(
    #     "--tiff",
    #     "-t",
    #     required=True,
    #     help="Directory containing .tif files",
    # )
    # parser.add_argument(
    #     "--shp",
    #     "-s",
    #     required=True,
    #     help="Directory containing .shp files",
    # )
    # parser.add_argument("--output", "-o", required=True, help="Path to output COCO JSON file")
    # args = parser.parse_args()
    # convert_shp_to_coco(args.tiff, args.shp, args.output)

    # --tiff C:\Users\<USER>\source\tobacco_det_data\batch2\images\train 
    # --shp C:\Users\<USER>\Desktop\tabacco_det_batch2\data 
    # -o C:\Users\<USER>\source\tobacco_det_data\batch2\images\train.json
    convert_shp_to_coco(
        r"C:\Users\<USER>\source\tobacco_det_data\data\images\val",
        r"C:\Users\<USER>\source\tobacco_det_data\data\all_data",
        r"C:\Users\<USER>\source\tobacco_det_data\data\annotations\val.json",
    )


if __name__ == "__main__":
    main()
